-- Function to get venue daily summary data for MSG91 WhatsApp template
-- This function aggregates all metrics needed for the daily_venue_report template
-- Compatible with ViaSocket PostgreSQL integration

CREATE OR REPLACE FUNCTION public.get_venue_daily_summary(
  p_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
  venue_id UUID,
  venue_name TEXT,
  admin_phone TEXT,
  admin_name TEXT,
  report_date TEXT,
  total_bookings TEXT,
  confirmed_bookings TEXT,
  cancelled_bookings TEXT,
  gross_revenue TEXT,
  net_revenue TEXT,
  coupon_usage_count TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_platform_fee_percentage NUMERIC := 5.0; -- Default platform fee percentage
BEGIN
  RETURN QUERY
  WITH venue_metrics AS (
    -- Get daily booking metrics per venue
    SELECT 
      v.id as venue_id,
      v.name as venue_name,
      COUNT(b.id) as total_bookings_count,
      COUNT(CASE WHEN b.status = 'confirmed' THEN 1 END) as confirmed_bookings_count,
      COUNT(CASE WHEN b.status = 'cancelled' THEN 1 END) as cancelled_bookings_count,
      COALESCE(SUM(CASE WHEN b.status IN ('confirmed', 'completed') THEN b.total_price ELSE 0 END), 0) as gross_revenue_amount,
      COALESCE(SUM(CASE WHEN b.status IN ('confirmed', 'completed') THEN 
        b.total_price * (1 - v_platform_fee_percentage / 100) ELSE 0 END), 0) as net_revenue_amount
    FROM venues v
    LEFT JOIN courts c ON v.id = c.venue_id AND c.is_active = true
    LEFT JOIN bookings b ON c.id = b.court_id AND b.booking_date = p_date
    WHERE v.is_active = true
    GROUP BY v.id, v.name
  ),
  coupon_metrics AS (
    -- Get daily coupon usage per venue
    SELECT 
      v.id as venue_id,
      COUNT(cu.id) as coupon_usage_count
    FROM venues v
    LEFT JOIN courts c ON v.id = c.venue_id AND c.is_active = true
    LEFT JOIN bookings b ON c.id = b.court_id AND b.booking_date = p_date
    LEFT JOIN coupon_usage cu ON b.id = cu.booking_id AND DATE(cu.created_at) = p_date
    WHERE v.is_active = true
    GROUP BY v.id
  ),
  admin_contacts AS (
    -- Get venue admin contact information
    SELECT 
      va.venue_id,
      p.phone as admin_phone,
      p.full_name as admin_name,
      ROW_NUMBER() OVER (PARTITION BY va.venue_id ORDER BY va.created_at) as rn
    FROM venue_admins va
    JOIN profiles p ON va.user_id = p.id
    WHERE p.phone IS NOT NULL 
      AND p.phone != '' 
      AND p.phone != '000000000'
      AND p.phone_verified = true
  )
  SELECT 
    vm.venue_id,
    vm.venue_name,
    ac.admin_phone,
    ac.admin_name,
    TO_CHAR(p_date, 'DD/MM/YYYY') as report_date,
    vm.total_bookings_count::TEXT as total_bookings,
    vm.confirmed_bookings_count::TEXT as confirmed_bookings,
    vm.cancelled_bookings_count::TEXT as cancelled_bookings,
    '₹' || TO_CHAR(vm.gross_revenue_amount, 'FM999,999,990') as gross_revenue,
    '₹' || TO_CHAR(vm.net_revenue_amount, 'FM999,999,990') as net_revenue,
    COALESCE(cm.coupon_usage_count, 0)::TEXT as coupon_usage_count
  FROM venue_metrics vm
  LEFT JOIN coupon_metrics cm ON vm.venue_id = cm.venue_id
  LEFT JOIN admin_contacts ac ON vm.venue_id = ac.venue_id AND ac.rn = 1
  WHERE ac.admin_phone IS NOT NULL
    AND (vm.total_bookings_count > 0 OR vm.gross_revenue_amount > 0) -- Only send reports for venues with activity
  ORDER BY vm.venue_name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_venue_daily_summary(DATE) TO authenticated;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_date_status ON bookings(booking_date, status);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_created_at ON coupon_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_venue_admins_venue_id ON venue_admins(venue_id);
